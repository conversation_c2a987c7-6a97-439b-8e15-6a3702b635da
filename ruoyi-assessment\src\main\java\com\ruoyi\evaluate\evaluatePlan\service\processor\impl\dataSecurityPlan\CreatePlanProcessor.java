package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.*;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.process.domain.ProcessStepInstance;

/**
 * 数据资产识别步骤数据处理器
 * <p>
 * 处理数据资产识别步骤的数据获取和处理逻辑
 * 包括数据资产清单、分类统计、识别进度等信息
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class CreatePlanProcessor extends AbstractStepDataProcessor {

    @Override
    public String getStepCode() {
        return "create_plan";
    }

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.FORM.getCode();
    }

    @Override
    protected String getStepName() {
        return "创建评估计划";
    }

    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        // 1. 根据stepCode和processInstanceId查找对应的stepInstance
        ProcessStepInstance stepInstance = findStepInstanceByCode(stepCode, processInstanceId);

        // 2. 使用实体类构建响应
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(planTask, stepInstance, stepCode, getEvaluateType());

        // 3. 添加特定的扩展数据
        getSpecificStepData(planTask, stepInstance, processInstanceId, response);

        // 4. 转换为Map格式（保持向后兼容）
        return response;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        return true;
    }

    private EvaluatePlanTaskDto getTaskInfo(EvaluatePlanTask planTask) {
        EvaluatePlanTaskDto planTaskDto = new EvaluatePlanTaskDto();
        BeanUtils.copyProperties(planTask, planTaskDto);
        return planTaskDto;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        response.addStepData("baseInfo", getTaskInfo(planTask));
    }

    /**
     * 获取暂存数据
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     *
     * @param planId   计划ID
     * @param stepCode 步骤编码
     * @return 暂存数据结果
     */
    @Override
    public StepDataResponse getDraftData(Long planId, String stepCode) {
        log.warn("使用默认处理器获取暂存数据: {}, 计划ID: {}", stepCode, planId);

        // 构建默认的暂存数据响应
        StepDataResponse response = StepDataResponse.builder()
                .stepCode(stepCode)
                .stepName(getStepName())
                .evaluateType(getEvaluateType())
                .build();

        // 添加默认暂存数据信息
        response.addStepData("message", "该步骤使用默认处理器，暂存功能不可用111，建议创建专用处理器");

        return response;
    }
}
