package com.ruoyi.evaluate.evaluatePlan.service.impl;

import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.IDraftStorageService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.DraftStepRequest;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanDraftService;
import com.ruoyi.evaluate.evaluatePlan.service.IEvaluatePlanTaskService;
import com.ruoyi.evaluate.evaluatePlan.service.processor.IStepDataProcessor;
import com.ruoyi.evaluate.evaluatePlan.service.processor.StepDataProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 评估计划暂存服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EvaluatePlanDraftServiceImpl implements IEvaluatePlanDraftService {

    private final IDraftStorageService draftStorageService;
    private final IEvaluatePlanTaskService evaluatePlanTaskService;
    private final StepDataProcessorFactory stepDataProcessorFactory;

    @Override
    public Map<String, Object> saveDraft(Long userId, SaveStepRequest request) {
        try {
            // 参数校验
            validateSaveRequest(request);

            // 获取评估计划任务以获得评估类型
            EvaluatePlanTask planTask = getEvaluatePlanTask(request.getPlanId());
            String evaluateType = planTask.getEvaluateType();
            if (StringUtils.isEmpty(evaluateType)) {
                log.warn("评估计划任务的评估类型为空，任务ID: {}", request.getPlanId());
                throw new ServiceException("评估计划任务的评估类型不能为空");
            }

            // 获取步骤数据处理器
            IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, request.getStepCode());

            log.debug("保存暂存数据，用户ID: {}, 步骤: {}, 数据类型: {}", userId, request.getStepCode(), processor.getDraftDataType());

            // 委托给处理器处理暂存数据保存
            return processor.saveDraftData(userId, request.getPlanId(), request.getStepCode(), request.getStepData());

        } catch (Exception e) {
            log.error("暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public StepDataResponse getDraft(Long userId, DraftStepRequest request) {
        try {
            // 参数校验
            validateStepRequest(request);

            // 获取评估计划任务以获得评估类型
            EvaluatePlanTask planTask = getEvaluatePlanTask(request.getPlanId());
            String evaluateType = planTask.getEvaluateType();
            if (StringUtils.isEmpty(evaluateType)) {
                log.warn("评估计划任务的评估类型为空，任务ID: {}", request.getPlanId());
                throw new ServiceException("评估计划任务的评估类型不能为空");
            }

            // 获取步骤数据处理器
            IStepDataProcessor processor = stepDataProcessorFactory.getProcessor(evaluateType, request.getStepCode());

            log.debug("获取暂存数据，用户ID: {}, 步骤: {}, 数据类型: {}", userId, request.getStepCode(), processor.getDraftDataType());

            // 委托给处理器处理暂存数据获取（移除userId参数，由处理器内部获取）
            return processor.getDraftData(request.getPlanId(), request.getStepCode());

        } catch (Exception e) {
            log.error("获取暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("获取暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> deleteDraft(Long userId, DraftStepRequest request) {
        try {
            // 参数校验
            validateStepRequest(request);
            
            String draftKey = request.generateDraftKey();
            log.info("删除暂存数据，用户ID: {}, 暂存键: {}", userId, draftKey);
            
            boolean success = draftStorageService.deleteDraft(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKey
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("draftKey", draftKey);
            result.put("planId", request.getPlanId());
            result.put("step", request.getStepCode());
            result.put("success", success);
            result.put("description", request.getDescription());
            
            if (success) {
                log.info("删除暂存数据成功，用户ID: {}, 暂存键: {}", userId, draftKey);
            } else {
                log.info("暂存数据不存在或已删除，用户ID: {}, 暂存键: {}", userId, draftKey);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("删除暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("删除暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> existsDraft(Long userId, DraftStepRequest request) {
        try {
            // 参数校验
            validateStepRequest(request);
            
            String draftKey = request.generateDraftKey();
            log.debug("检查暂存数据是否存在，用户ID: {}, 暂存键: {}", userId, draftKey);
            
            boolean exists = draftStorageService.existsDraft(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKey
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("draftKey", draftKey);
            result.put("planId", request.getPlanId());
            result.put("step", request.getStepCode());
            result.put("exists", exists);
            result.put("description", request.getDescription());
            
            if (exists) {
                // 获取剩余过期时间
                long ttl = draftStorageService.getDraftTtl(
                        DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                        userId,
                        draftKey
                );
                result.put("ttl", ttl);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("检查暂存数据异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("检查暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> createFromDraft(Long userId, DraftStepRequest request, boolean deleteDraft) {
        try {
            // 参数校验
            validateStepRequest(request);
            
            String draftKey = request.generateDraftKey();
            log.info("从暂存数据创建任务，用户ID: {}, 暂存键: {}, 删除暂存: {}", userId, draftKey, deleteDraft);
            
            // 获取暂存数据
            EvaluatePlanTaskDto taskDto = draftStorageService.getDraft(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKey,
                    EvaluatePlanTaskDto.class
            );
            
            if (taskDto == null) {
                throw new ServiceException("暂存数据不存在或已过期");
            }
            
            // 创建评估计划任务
            Long taskId = evaluatePlanTaskService.createPlanTask(taskDto);
            
            // 可选删除暂存数据
            boolean draftDeleted = false;
            if (deleteDraft) {
                draftDeleted = draftStorageService.deleteDraft(
                        DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                        userId,
                        draftKey
                );
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", taskId);
            result.put("draftKey", draftKey);
            result.put("planId", request.getPlanId());
            result.put("step", request.getStepCode());
            result.put("draftDeleted", draftDeleted);
            result.put("description", request.getDescription());
            
            log.info("从暂存数据创建任务成功，用户ID: {}, 任务ID: {}, 暂存键: {}", userId, taskId, draftKey);
            
            return result;
            
        } catch (Exception e) {
            log.error("从暂存数据创建任务异常，用户ID: {}, 请求: {}, 错误: {}", userId, request, e.getMessage(), e);
            throw new ServiceException("从暂存数据创建任务失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> deleteStepDraft(Long userId, Long planId, String previousStep) {
        try {
            // 参数校验
            if (planId == null || planId <= 0) {
                throw new IllegalArgumentException("评估计划ID不能为空且必须为正数");
            }
            if (previousStep == null || previousStep.trim().isEmpty()) {
                throw new IllegalArgumentException("步骤名称不能为空");
            }

            DraftStepRequest request = new DraftStepRequest()
                    .setPlanId(planId)
                    .setStepCode(previousStep);

            String draftKey = request.generateDraftKey();
            log.info("删除上一步暂存数据，用户ID: {}, 暂存键: {}", userId, draftKey);

            boolean success = draftStorageService.deleteDraft(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKey
            );

            Map<String, Object> result = new HashMap<>();
            result.put("deletedDraftKey", draftKey);
            result.put("planId", planId);
            result.put("previousStep", previousStep);
            result.put("success", success);

            return result;

        } catch (Exception e) {
            log.error("删除上一步暂存数据异常，用户ID: {}, 计划ID: {}, 步骤: {}, 错误: {}",
                    userId, planId, previousStep, e.getMessage(), e);
            throw new ServiceException("删除上一步暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> batchGetDrafts(Long userId, Long planId, List<String> steps) {
        try {
            // 参数校验
            if (planId == null || planId <= 0) {
                throw new IllegalArgumentException("评估计划ID不能为空且必须为正数");
            }
            if (steps == null || steps.isEmpty()) {
                throw new IllegalArgumentException("步骤列表不能为空");
            }

            log.info("批量获取暂存数据，用户ID: {}, 计划ID: {}, 步骤数: {}", userId, planId, steps.size());

            // 根据planId和步骤列表生成暂存键列表
            List<String> draftKeys = new java.util.ArrayList<>();
            for (String step : steps) {
                draftKeys.add(planId + "_" + step);
            }

            Map<String, EvaluatePlanTaskDto> results = draftStorageService.batchGetDrafts(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKeys,
                    EvaluatePlanTaskDto.class
            );

            Map<String, Object> result = new HashMap<>();
            result.put("planId", planId);
            result.put("steps", steps);
            result.put("data", results);
            result.put("foundCount", results.size());
            result.put("totalCount", steps.size());

            return result;

        } catch (Exception e) {
            log.error("批量获取暂存数据异常，用户ID: {}, 计划ID: {}, 错误: {}", userId, planId, e.getMessage(), e);
            throw new ServiceException("批量获取暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> batchDeleteDrafts(Long userId, Long planId, List<String> steps) {
        try {
            // 参数校验
            if (planId == null || planId <= 0) {
                throw new IllegalArgumentException("评估计划ID不能为空且必须为正数");
            }
            if (steps == null || steps.isEmpty()) {
                throw new IllegalArgumentException("步骤列表不能为空");
            }

            log.info("批量删除暂存数据，用户ID: {}, 计划ID: {}, 步骤数: {}", userId, planId, steps.size());

            // 根据planId和步骤列表生成暂存键列表
            List<String> draftKeys = new java.util.ArrayList<>();
            for (String step : steps) {
                draftKeys.add(planId + "_" + step);
            }

            int count = draftStorageService.batchDeleteDrafts(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    draftKeys
            );

            Map<String, Object> result = new HashMap<>();
            result.put("planId", planId);
            result.put("steps", steps);
            result.put("deletedCount", count);
            result.put("totalCount", steps.size());

            return result;

        } catch (Exception e) {
            log.error("批量删除暂存数据异常，用户ID: {}, 计划ID: {}, 错误: {}", userId, planId, e.getMessage(), e);
            throw new ServiceException("批量删除暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Map<String, Object>> getUserDraftSummary(Long userId) {
        try {
            log.debug("获取用户暂存数据摘要，用户ID: {}", userId);

            return draftStorageService.getUserDraftSummary(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId
            );

        } catch (Exception e) {
            log.error("获取用户暂存数据摘要异常，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new ServiceException("获取用户暂存数据摘要失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> clearUserDrafts(Long userId) {
        try {
            log.info("清理用户所有暂存数据，用户ID: {}", userId);

            int count = draftStorageService.clearUserDrafts(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId
            );

            Map<String, Object> result = new HashMap<>();
            result.put("clearedCount", count);

            return result;

        } catch (Exception e) {
            log.error("清理用户暂存数据异常，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new ServiceException("清理用户暂存数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> batchSaveDrafts(Long userId, Long planId, Map<String, Object> drafts) {
        try {
            // 参数校验
            if (planId == null || planId <= 0) {
                throw new IllegalArgumentException("评估计划ID不能为空且必须为正数");
            }
            if (drafts == null || drafts.isEmpty()) {
                throw new IllegalArgumentException("暂存数据不能为空");
            }

            log.info("批量暂存数据，用户ID: {}, 计划ID: {}, 数据条数: {}", userId, planId, drafts.size());

            // 转换为带planId前缀的暂存键
            Map<String, Object> objectDrafts = new HashMap<>();
            for (Map.Entry<String, Object> entry : drafts.entrySet()) {
                String draftKey = planId + "_" + entry.getKey();
                objectDrafts.put(draftKey, entry.getValue());
            }

            int count = draftStorageService.batchSaveDrafts(
                    DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                    userId,
                    objectDrafts,
                    365 * 24, // 365天
                    TimeUnit.HOURS
            );

            Map<String, Object> result = new HashMap<>();
            result.put("planId", planId);
            result.put("savedCount", count);
            result.put("totalCount", drafts.size());

            return result;

        } catch (Exception e) {
            log.error("批量暂存数据异常，用户ID: {}, 计划ID: {}, 错误: {}", userId, planId, e.getMessage(), e);
            throw new ServiceException("批量暂存数据失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验保存请求参数
     */
    private void validateSaveRequest(SaveStepRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效：" + request.getDescription());
        }
    }

    /**
     * 校验步骤请求参数
     */
    private void validateStepRequest(DraftStepRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException("请求参数无效：" + request.getDescription());
        }
    }

    /**
     * 获取评估计划任务
     */
    private EvaluatePlanTask getEvaluatePlanTask(Long planId) {
        EvaluatePlanTask planTask = evaluatePlanTaskService.getById(planId);
        if (planTask == null) {
            throw new ServiceException("评估计划任务不存在，ID: " + planId);
        }
        return planTask;
    }
}
