package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.dataSecurityPlan;

import java.util.*;

import com.ruoyi.common.constant.DraftConstants;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.EvaluatePlanTaskDto;
import com.ruoyi.evaluate.evaluatePlan.enums.DraftDataTypeEnum;
import com.ruoyi.evaluate.evaluatePlan.util.StepDataConverter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.service.processor.AbstractStepDataProcessor;
import com.ruoyi.process.domain.ProcessStepInstance;

/**
 * 风险识别分析步骤数据处理器
 * <p>
 * 处理风险识别分析步骤的数据获取和处理逻辑
 * 包括风险清单、风险分类、威胁分析等信息
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class EvaluateScopeProcessor extends AbstractStepDataProcessor {

    @Override
    public String getStepCode() {
        return "evaluate_scope";
    }

    @Override
    public String getEvaluateType() {
        return "data_security_plan";
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return DraftDataTypeEnum.FORM.getCode();
    }

    @Override
    protected String getStepName() {
        return "确定评估范围";
    }


    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        // 1. 根据stepCode和processInstanceId查找对应的stepInstance
        ProcessStepInstance stepInstance = findStepInstanceByCode(stepCode, processInstanceId);

        // 2. 使用实体类构建响应
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(
                planTask, stepInstance, stepCode, getEvaluateType());

        // 3. 添加特定的扩展数据
        /*if (stepInstance != null) {
            response.setMessage("步骤实例已找到");
            // 将具体步骤数据作为扩展数据添加
            Map<String, Object> specificData = processSpecificStepData(planTask, stepInstance, processInstanceId);
            response.addExtensionData("data", specificData);
        } else {
            response.setMessage("未找到对应的步骤实例");
            response.addExtensionData("data", new HashMap<>());
        }*/


        // 4. 转换为Map格式（保持向后兼容）
        return response;
    }

    /**
     * 保存步骤数据
     *
     * @param planTask          评估计划任务
     * @param stepCode          步骤编码
     * @param stepData          步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        return false;
    }

    /**
     * 处理具体步骤数据（由子类实现）
     *
     * @param planTask          评估计划任务
     * @param stepInstance      步骤实例
     * @param processInstanceId 流程实例ID
     * @param response          步骤数据响应对象，子类在此基础上添加扩展数据
     */
    @Override
    protected void getSpecificStepData(EvaluatePlanTask planTask, ProcessStepInstance stepInstance, Long processInstanceId, StepDataResponse response) {
        log.debug("类型安全的数据资产识别步骤数据处理完成，资产数量: {}", 1);
    }

    /**
     * 重写获取暂存数据方法，提供特定的评估范围暂存数据处理
     * 参考 getStepData 方法的实现风格
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     */
    @Override
    protected StepDataResponse getFormDraftData(Long userId, EvaluatePlanTask planTask, String stepCode) {
        // 1. 生成暂存键并记录日志
        String draftKey = planTask.getId() + "_" + stepCode;
        log.debug("获取评估范围暂存数据，用户ID: {}, 计划ID: {}, 步骤: {}, 暂存键: {}",
                userId, planTask.getId(), stepCode, draftKey);

        // 2. 获取暂存的表单数据
        EvaluatePlanTaskDto taskDto = draftStorageService.getDraft(
                DraftConstants.BUSINESS_TYPE_EVALUATE_PLAN_TASK,
                userId,
                draftKey,
                EvaluatePlanTaskDto.class
        );

        // 3. 使用实体类构建响应，参考 getStepData 的结构
        StepDataResponse response = StepDataConverter.buildBaseStepDataResponse(
                planTask, null, stepCode, getEvaluateType());

        // 4. 添加基础暂存数据信息
        response.addStepData("data", taskDto);

        // 6. 返回构建的响应
        return response;
    }


}
