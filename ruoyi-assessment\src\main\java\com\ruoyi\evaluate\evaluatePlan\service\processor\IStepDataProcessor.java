package com.ruoyi.evaluate.evaluatePlan.service.processor;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;

import java.util.Map;

/**
 * 步骤数据处理器接口
 * 
 * 用于处理不同步骤的数据获取和处理逻辑
 * 每个步骤的处理逻辑和数据结构都不一样，通过实现此接口来支持拆分单独处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IStepDataProcessor {

    /**
     * 获取支持的步骤编码
     *
     * @return 步骤编码
     */
    String getStepCode();

    /**
     * 获取支持的评估类型（可选，返回null表示支持所有类型）
     *
     * @return 评估类型
     */
    String getEvaluateType();

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    String getDraftDataType();

    /**
     * 处理步骤数据（获取数据）
     *
     * @param planTask 评估计划任务
     * @param stepCode 步骤编码
     * @param processInstanceId 流程实例ID
     * @return 步骤数据响应
     */
    StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId);

    /**
     * 保存步骤数据
     *
     * @param planTask 评估计划任务
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @param processInstanceId 流程实例ID
     * @return 保存是否成功
     */
    boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId);

    /**
     * 检查是否支持指定的步骤和评估类型
     *
     * @param evaluateType 评估类型
     * @param stepCode 步骤编码
     * @return 是否支持
     */
    default boolean supports(String evaluateType, String stepCode) {
        boolean stepMatches = getStepCode() != null && getStepCode().equals(stepCode);
        boolean typeMatches = getEvaluateType() == null || getEvaluateType().equals(evaluateType);
        return stepMatches && typeMatches;
    }

    /**
     * 获取暂存数据
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     *
     * @param planId 计划ID
     * @param stepCode 步骤编码
     * @return 暂存数据结果
     */
    StepDataResponse getDraftData(Long planId, String stepCode);

    /**
     * 保存暂存数据
     *
     * @param userId 用户ID
     * @param planId 计划ID
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @return 保存结果
     */
    Map<String, Object> saveDraftData(Long userId, Long planId, String stepCode, Object stepData);

    /**
     * 是否支持暂存功能
     *
     * @return true-支持暂存，false-不支持暂存
     */
    default boolean supportsDraft() {
        return getDraftDataType() != null;
    }
}
