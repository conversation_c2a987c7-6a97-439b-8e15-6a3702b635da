package com.ruoyi.evaluate.evaluatePlan.service;

import com.ruoyi.evaluate.evaluatePlan.domain.dto.StepDataResponse;
import com.ruoyi.evaluate.evaluatePlan.dto.SaveStepRequest;

import java.util.Map;

/**
 * 评估计划步骤数据Service接口
 * 
 * 用于处理评估计划中不同步骤的数据获取和处理
 * 每个步骤的处理逻辑和数据结构都不一样，支持拆分单独处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IEvaluatePlanStepDataService {

    /**
     * 获取评估计划指定步骤的数据
     *
     * @param planId 评估计划ID
     * @param stepCode 步骤编码
     * @return 步骤数据
     */
    StepDataResponse getStepData(Long planId, String stepCode);

    /**
     * 保存当前步骤数据
     *
     * @param draftSaveRequest 步骤数据保存请求
     * @return 保存结果
     */
    Boolean saveCurrentStepData(SaveStepRequest draftSaveRequest);

    /**
     * 暂存当前步骤数据（不进行严格校验）
     *
     * @param draftSaveRequest 步骤数据暂存请求
     * @return 暂存结果
     */
    Boolean draftCurrentStepData(SaveStepRequest draftSaveRequest);

    /**
     * 标记下一个步骤的开始时间
     *
     * @param processInstanceId 流程实例ID
     * @param operator 操作人
     */
    void markNextStepStartTime(Long processInstanceId, String operator);

    /**
     * 获取评估计划步骤暂存数据
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     *
     * @param planId 评估计划ID
     * @param stepCode 步骤编码
     * @return 暂存数据结果
     */
    StepDataResponse getDraftData(Long planId, String stepCode);

    /**
     * 暂存评估计划步骤数据
     *
     * @param userId 用户ID
     * @param request 暂存请求参数
     * @return 暂存结果
     */
    Map<String, Object> saveDraftData(Long userId, SaveStepRequest request);

}
