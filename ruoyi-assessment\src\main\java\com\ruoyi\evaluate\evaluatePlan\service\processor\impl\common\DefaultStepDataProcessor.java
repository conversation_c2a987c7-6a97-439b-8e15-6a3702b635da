package com.ruoyi.evaluate.evaluatePlan.service.processor.impl.common;

import java.util.*;

import com.ruoyi.common.service.IDraftStorageService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.ruoyi.evaluate.evaluatePlan.domain.EvaluatePlanTask;
import com.ruoyi.evaluate.evaluatePlan.domain.dto.*;
import com.ruoyi.evaluate.evaluatePlan.service.processor.IStepDataProcessor;

/**
 * 默认步骤数据处理器
 * <p>
 * 用于处理没有特定处理器的步骤，提供通用的数据处理逻辑
 * 优先级最低，作为兜底处理器
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
@Order(Integer.MAX_VALUE) // 最低优先级
public class DefaultStepDataProcessor implements IStepDataProcessor {

    @Override
    public String getStepCode() {
        // 返回null表示这是一个通用处理器
        return null;
    }

    @Override
    public String getEvaluateType() {
        // 返回null表示支持所有评估类型
        return null;
    }

    /**
     * 获取暂存数据类型（表单数据 or 纯列表）
     *
     * @return 暂存数据类型
     */
    @Override
    public String getDraftDataType() {
        return null;
    }

    @Override
    public boolean supports(String stepCode, String evaluateType) {
        // 默认处理器支持所有步骤，但优先级最低
        return true;
    }

    /**
     * 获取暂存数据
     * 结合getStepData方法的设计，移除userId参数，从SecurityUtils获取当前用户ID
     *
     * @param planId   计划ID
     * @param stepCode 步骤编码
     * @return 暂存数据结果
     */
    @Override
    public StepDataResponse getDraftData(Long planId, String stepCode) {
        log.warn("使用默认处理器获取暂存数据: {}, 计划ID: {}", stepCode, planId);

        // 构建默认的暂存数据响应
        StepDataResponse response = StepDataResponse.builder()
                .stepCode(stepCode)
                .stepName(getStepNameByCode(stepCode))
                .evaluateType("unknown")
                .build();

        // 添加默认暂存数据信息
        response.addStepData("data", null);
        response.addStepData("message", "该步骤使用默认处理器，暂存功能不可用，建议创建专用处理器");

        return response;
    }

    /**
     * 保存暂存数据
     *
     * @param userId   用户ID
     * @param planId   计划ID
     * @param stepCode 步骤编码
     * @param stepData 步骤数据
     * @return 保存结果
     */
    @Override
    public Map<String, Object> saveDraftData(Long userId, Long planId, String stepCode, Object stepData) {
        log.warn("使用默认处理器保存暂存数据: {}, 计划ID: {}, 用户ID: {}", stepCode, planId, userId);

        // 默认处理器不支持暂存功能
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("description", String.format("评估计划[%d]的步骤[%s]", planId, stepCode));
        result.put("message", "该步骤使用默认处理器，暂存功能不可用，建议创建专用处理器");

        return result;
    }

    @Override
    public StepDataResponse getStepData(EvaluatePlanTask planTask, String stepCode, Long processInstanceId) {
        log.info("使用默认处理器处理步骤数据: {}, 计划ID: {}", stepCode, planTask.getId());

        StepDataResponse response = StepDataResponse.builder()
                .stepCode(stepCode)
                .stepName(getStepNameByCode(stepCode))
                .planTask(planTask)
                .evaluateType(planTask.getEvaluateType())
                .build();

        // 通用数据
        response.addStepData("message", "该步骤使用默认处理器，建议创建专用处理器以获得更好的数据支持");
        response.addStepData("dataAvailable", false);
        response.addStepData("processorType", "default");
        response.addStepData("processInstanceId", processInstanceId);

        // 模拟基础统计
        Map<String, Object> basicStats = new HashMap<>();
        basicStats.put("status", "使用默认处理");
        basicStats.put("dataQuality", "基础");
        basicStats.put("completeness", "部分");
        response.addStepData("statistics", basicStats);

        return response;
    }

    @Override
    public boolean saveStepData(EvaluatePlanTask planTask, String stepCode, Object stepData, Long processInstanceId) {
        log.info("使用默认处理器保存步骤数据: {}, 计划ID: {}, 流程实例ID: {}",
                stepCode, planTask.getId(), processInstanceId);

        // 默认实现：记录日志，表示数据已接收
        log.info("步骤数据内容: {}", stepData);

        // 在实际的processor实现中，这里应该包含具体的保存逻辑
        // 例如：保存到数据库、调用外部服务、文件存储等

        return true;
    }

    /**
     * 根据步骤编码获取步骤名称
     */
    private String getStepNameByCode(String stepCode) {
        if (stepCode == null) {
            return "未知步骤";
        }

        Map<String, String> stepNames = new HashMap<>();
        stepNames.put("data_asset_identify", "数据资产识别");
        stepNames.put("data_classify", "数据分类分级");
        stepNames.put("risk_identify", "风险识别分析");
        stepNames.put("risk_assessment", "风险等级评定");
        stepNames.put("control_measures", "控制措施建议");
        stepNames.put("report_generate", "报告生成");

        return stepNames.getOrDefault(stepCode, stepCode);
    }
}
